import { DOMUtils } from '../utils/dom.js';
import { Validator, VALIDATION_RULES } from '../utils/validation.js';
import authService from '../services/authService.js';

export class ProfilePage {
  constructor(containerId, onLogout) {
    this.container = DOMUtils.getElementById(containerId);
    this.onLogout = onLogout;
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
  }

  render() {
    const user = authService.getCurrentUser();
    const content = `
      <div class="min-h-screen bg-gray-100">
        <div class="max-w-6xl mx-auto p-8">
          <div class="bg-white rounded-lg shadow-md">
            <div class="flex justify-between items-center p-6 border-b">
              <div>
                <h2 class="text-2xl font-bold text-gray-800">Profile Dashboard</h2>
                <p class="text-gray-600">Manage your account and preferences</p>
              </div>
              <button id="logoutBtn" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">Logout</button>
            </div>
            
            <div class="p-6">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- User Info -->
                <div class="bg-gray-50 p-6 rounded-lg">
                  <h3 class="text-lg font-semibold mb-4">User Information</h3>
                  <div class="space-y-3">
                    <p><span class="font-medium">Email:</span> ${user?.email || 'Not provided'}</p>
                    <p><span class="font-medium">Username:</span> ${user?.username || 'Not set'}</p>
                    <p><span class="font-medium">Full Name:</span> ${user?.full_name || 'Not set'}</p>
                    <p><span class="font-medium">Date of Birth:</span> ${user?.date_of_birth || 'Not set'}</p>
                    <p><span class="font-medium">Gender:</span> ${user?.gender || 'Not set'}</p>
                    <p><span class="font-medium">School ID:</span> ${user?.school_id || 'Not set'}</p>
                  </div>
                </div>
                
                <!-- Update Profile Form -->
                <div>
                  <h3 class="text-lg font-semibold mb-4">Update Profile</h3>
                  <form id="profileForm" class="space-y-4">
                    <div class="mb-6">
                      <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                      <input type="text" id="username" name="username" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" value="${user?.username || ''}">
                      <div id="usernameError" class="text-red-600 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div class="mb-6">
                      <label for="fullName" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                      <input type="text" id="fullName" name="full_name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" value="${user?.full_name || ''}">
                      <div id="fullNameError" class="text-red-600 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div class="mb-6">
                      <label for="dateOfBirth" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                      <input type="date" id="dateOfBirth" name="date_of_birth" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" value="${user?.date_of_birth || ''}">
                      <div id="dateOfBirthError" class="text-red-600 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div class="mb-6">
                      <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                      <select id="gender" name="gender" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50">
                        <option value="">Select Gender</option>
                        <option value="male" ${user?.gender === 'male' ? 'selected' : ''}>Male</option>
                        <option value="female" ${user?.gender === 'female' ? 'selected' : ''}>Female</option>
                        <option value="other" ${user?.gender === 'other' ? 'selected' : ''}>Other</option>
                      </select>
                      <div id="genderError" class="text-red-600 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div class="mb-6">
                      <label for="schoolId" class="block text-sm font-medium text-gray-700 mb-2">School ID</label>
                      <input type="text" id="schoolId" name="school_id" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" value="${user?.school_id || ''}">
                      <div id="schoolIdError" class="text-red-600 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div id="profileError" class="text-red-600 text-sm mt-1 hidden"></div>
                    <div id="profileSuccess" class="text-green-600 text-sm mt-1 hidden"></div>
                    
                    <button type="submit" id="updateProfileBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full">
                      <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"/>
                      </svg>
                      Update Profile
                    </button>
                  </form>
                </div>
              </div>
              
              <!-- Change Password Section -->
              <div class="mt-8 pt-8 border-t">
                <h3 class="text-lg font-semibold mb-4">Change Password</h3>
                <div class="max-w-md">
                  <form id="changePasswordForm" class="space-y-4">
                    <div class="mb-6">
                      <label for="currentPassword" class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                      <input type="password" id="currentPassword" name="current_password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" placeholder="Enter current password" required>
                      <div id="currentPasswordError" class="text-red-600 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div class="mb-6">
                      <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                      <input type="password" id="newPassword" name="new_password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" placeholder="Enter new password" required>
                      <div id="newPasswordError" class="text-red-600 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div class="mb-6">
                      <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                      <input type="password" id="confirmPassword" name="confirm_password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" placeholder="Confirm new password" required>
                      <div id="confirmPasswordError" class="text-red-600 text-sm mt-1 hidden"></div>
                    </div>
                    
                    <div id="changePasswordError" class="text-red-600 text-sm mt-1 hidden"></div>
                    <div id="changePasswordSuccess" class="text-green-600 text-sm mt-1 hidden"></div>
                    
                    <button type="submit" id="changePasswordBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full">
                      <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                      </svg>
                      Change Password
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
    
    DOMUtils.setContent(this.container, content);
  }

  attachEventListeners() {
    // Logout button
    const logoutBtn = DOMUtils.getElementById('logoutBtn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', this.handleLogout.bind(this));
    }

    // Profile form
    const profileForm = DOMUtils.getElementById('profileForm');
    if (profileForm) {
      profileForm.addEventListener('submit', this.handleUpdateProfile.bind(this));
    }

    // Change password form
    const changePasswordForm = DOMUtils.getElementById('changePasswordForm');
    if (changePasswordForm) {
      changePasswordForm.addEventListener('submit', this.handleChangePassword.bind(this));
    }
  }

  async handleLogout() {
    try {
      await authService.logout();
      if (this.onLogout) {
        this.onLogout();
      }
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      if (this.onLogout) {
        this.onLogout();
      }
    }
  }

  async handleUpdateProfile(event) {
    event.preventDefault();
    const form = event.target;
    const submitBtn = DOMUtils.getElementById('updateProfileBtn');
    const errorEl = DOMUtils.getElementById('profileError');
    const successEl = DOMUtils.getElementById('profileSuccess');

    this.clearAllErrors();

    const formData = new FormData(form);
    const profileData = Object.fromEntries(formData.entries());
    
    // Validate form
    const validation = Validator.validateForm(profileData, {
      username: VALIDATION_RULES.username,
      full_name: VALIDATION_RULES.fullName,
      date_of_birth: VALIDATION_RULES.dateOfBirth,
      gender: VALIDATION_RULES.gender,
      school_id: VALIDATION_RULES.schoolId
    });
    
    if (!validation.isValid) {
      this.showFieldErrors('profile', validation.errors);
      return;
    }

    try {
      DOMUtils.setLoading(submitBtn, true);

      const result = await authService.updateProfile(profileData);

      if (result.success) {
        DOMUtils.showSuccess(successEl, 'Profile updated successfully!');
        // Re-render to show updated data
        setTimeout(() => {
          this.render();
          this.attachEventListeners();
        }, 1000);
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }

  async handleChangePassword(event) {
    event.preventDefault();
    const form = event.target;
    const submitBtn = DOMUtils.getElementById('changePasswordBtn');
    const errorEl = DOMUtils.getElementById('changePasswordError');
    const successEl = DOMUtils.getElementById('changePasswordSuccess');

    this.clearAllErrors();

    const formData = new FormData(form);
    const passwordData = Object.fromEntries(formData.entries());

    // Validate passwords match
    if (passwordData.new_password !== passwordData.confirm_password) {
      DOMUtils.showError(DOMUtils.getElementById('confirmPasswordError'), 'Passwords do not match');
      return;
    }

    // Validate new password
    const validation = Validator.validateForm(passwordData, {
      current_password: VALIDATION_RULES.password,
      new_password: VALIDATION_RULES.password
    });

    if (!validation.isValid) {
      this.showFieldErrors('changePassword', validation.errors);
      return;
    }

    try {
      DOMUtils.setLoading(submitBtn, true);

      const result = await authService.changePassword(
        passwordData.current_password,
        passwordData.new_password
      );

      if (result.success) {
        DOMUtils.showSuccess(successEl, 'Password changed successfully!');
        form.reset();
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }

  clearAllErrors() {
    const errorElements = this.container.querySelectorAll('.text-red-600, .text-green-600');
    errorElements.forEach(el => {
      el.classList.add('hidden');
      el.textContent = '';
    });
  }

  showFieldErrors(formType, errors) {
    Object.entries(errors).forEach(([field, message]) => {
      const errorEl = DOMUtils.getElementById(`${formType}${field.charAt(0).toUpperCase() + field.slice(1)}Error`) ||
                     DOMUtils.getElementById(`${field}Error`);
      if (errorEl) {
        DOMUtils.showError(errorEl, message);
      }
    });
  }
}
