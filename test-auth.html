<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input { padding: 8px; width: 300px; }
        button { padding: 10px 20px; margin: 5px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .error { background-color: #ffe6e6; border-color: #ff0000; }
        .success { background-color: #e6ffe6; border-color: #00ff00; }
    </style>
</head>
<body>
    <h1>Authentication Test</h1>
    
    <div>
        <h2>Register Test</h2>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" id="registerEmail" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="registerPassword" value="password123">
        </div>
        <button onclick="testRegister()">Test Register</button>
        <div id="registerResult" class="result" style="display: none;"></div>
    </div>
    
    <div>
        <h2>Login Test</h2>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" id="loginEmail" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="loginPassword" value="password123">
        </div>
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult" class="result" style="display: none;"></div>
    </div>
    
    <script type="module">
        import authService from './src/js/services/authService.js';
        import { Validator, VALIDATION_RULES } from './src/js/utils/validation.js';
        
        window.testRegister = async function() {
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const resultDiv = document.getElementById('registerResult');
            
            try {
                // Test validation first
                const validation = Validator.validateForm({ email, password }, {
                    email: VALIDATION_RULES.email,
                    password: VALIDATION_RULES.password
                });
                
                if (!validation.isValid) {
                    throw new Error('Validation failed: ' + JSON.stringify(validation.errors));
                }
                
                const result = await authService.register(email, password);
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `<strong>Success:</strong> ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
            resultDiv.style.display = 'block';
        };
        
        window.testLogin = async function() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                // Test validation first
                const validation = Validator.validateForm({ email, password }, {
                    email: VALIDATION_RULES.email,
                    password: VALIDATION_RULES.password
                });
                
                if (!validation.isValid) {
                    throw new Error('Validation failed: ' + JSON.stringify(validation.errors));
                }
                
                const result = await authService.login(email, password);
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `<strong>Success:</strong> ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
            resultDiv.style.display = 'block';
        };
    </script>
</body>
</html>
