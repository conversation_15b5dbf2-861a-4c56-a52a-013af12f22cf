import { DOMUtils } from '../utils/dom.js';
import { Validator, VALIDATION_RULES } from '../utils/validation.js';
import authService from '../services/authService.js';

export class AuthForm {
  constructor(containerId, onAuthSuccess) {
    this.container = DOMUtils.getElementById(containerId);
    this.currentForm = 'login'; // 'login', 'register'
    this.onAuthSuccess = onAuthSuccess;
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
  }

  render() {
    const content = this.renderAuthForm();
    DOMUtils.setContent(this.container, content);
  }

  // Render combined auth form (login + register)
  renderAuthForm() {
    return `
      <div class="min-h-screen flex">
        <!-- Left Side - Branding (70% width) -->
        <div class="bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 relative overflow-hidden" style="flex: 0 0 65%;">
          <div class="absolute inset-0 opacity-10" style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 2px, transparent 2px), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.2) 2px, transparent 2px); background-size: 50px 50px;"></div>
          <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full opacity-20 blur-3xl"></div>

          <div class="flex flex-col justify-center h-full p-12 relative z-10">
            <div class="text-white text-center relative z-10">
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-8 backdrop-blur-sm">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
              </div>
              <h1 class="text-4xl font-bold mb-4">Peta Talenta</h1>
              <p class="text-blue-200 text-lg mb-12">Platform Pemetaan dan Analisis Talenta Berbasis AI</p>
            </div>

            <div class="grid grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div class="text-center text-white">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 class="font-semibold mb-2">Asesmen Cerdas</h3>
                <p class="text-blue-200 text-sm">Algoritma AI canggih untuk evaluasi talenta yang akurat</p>
              </div>

              <div class="text-center text-white">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
                <h3 class="font-semibold mb-2">Wawasan Bertenaga AI</h3>
                <p class="text-blue-200 text-sm">Rekomendasi berbasis data untuk keputusan yang lebih baik</p>
              </div>

              <div class="text-center text-white">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 class="font-semibold mb-2">Analitik Real-time</h3>
                <p class="text-blue-200 text-sm">Pantau kemajuan dan kinerja secara real-time</p>
              </div>
            </div>

            <div class="text-center mt-12">
              <p class="text-blue-200 text-sm">"Temukan potensi terbaik Anda dengan Peta Talenta"</p>
            </div>
          </div>
        </div>

        <!-- Right Side - Auth Form (30% width) -->
        <div class="bg-white flex items-center justify-center p-6" style="flex: 0 0 35%;">
          <div class="w-full max-w-md">
            <div class="text-center mb-8">
              <div class="flex mb-8 bg-gray-100 rounded-lg p-1">
                <div class="flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer ${this.currentForm === 'login' ? 'bg-white text-blue-600 font-medium shadow-sm' : 'text-gray-600 hover:text-gray-800'}" id="loginTab">Masuk</div>
                <div class="flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer ${this.currentForm === 'register' ? 'bg-white text-blue-600 font-medium shadow-sm' : 'text-gray-600 hover:text-gray-800'}" id="registerTab">Daftar</div>
              </div>
              <h2 class="text-2xl font-bold text-gray-800 mb-2">${this.currentForm === 'login' ? 'Selamat Datang Kembali' : 'Buat Akun Anda'}</h2>
              <p class="text-gray-600">${this.currentForm === 'login' ? 'Masuk ke akun Anda untuk melanjutkan' : 'Bergabunglah dengan Peta Talenta hari ini'}</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="space-y-6 ${this.currentForm === 'login' ? '' : 'hidden'}">
              <div class="mb-6">
                <label for="loginEmail" class="block text-sm font-medium text-gray-700 mb-2">Alamat Email</label>
                <input type="email" id="loginEmail" name="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" placeholder="Masukkan email Anda" required>
                <div id="loginEmailError" class="text-red-600 text-sm mt-1 hidden"></div>
              </div>

              <div class="mb-6">
                <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-2">Kata Sandi</label>
                <input type="password" id="loginPassword" name="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" placeholder="Masukkan kata sandi Anda" required>
                <div id="loginPasswordError" class="text-red-600 text-sm mt-1 hidden"></div>
              </div>

              <div id="loginError" class="text-red-600 text-sm mt-1 hidden"></div>
              <div id="loginSuccess" class="text-green-600 text-sm mt-1 hidden"></div>

              <button type="submit" id="loginBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                Masuk
              </button>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="space-y-6 ${this.currentForm === 'register' ? '' : 'hidden'}">
              <div class="mb-6">
                <label for="registerEmail" class="block text-sm font-medium text-gray-700 mb-2">Alamat Email</label>
                <input type="email" id="registerEmail" name="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" placeholder="Masukkan email Anda" required>
                <div id="registerEmailError" class="text-red-600 text-sm mt-1 hidden"></div>
              </div>

              <div class="mb-6">
                <label for="registerPassword" class="block text-sm font-medium text-gray-700 mb-2">Kata Sandi</label>
                <input type="password" id="registerPassword" name="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" placeholder="Masukkan kata sandi Anda" required>
                <div id="registerPasswordError" class="text-red-600 text-sm mt-1 hidden"></div>
              </div>

              <div class="mb-6">
                <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">Konfirmasi Kata Sandi</label>
                <input type="password" id="confirmPassword" name="confirmPassword" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500" placeholder="Konfirmasi kata sandi Anda" required>
                <div id="confirmPasswordError" class="text-red-600 text-sm mt-1 hidden"></div>
              </div>

              <div class="flex items-center space-x-2 mb-6">
                <input type="checkbox" id="agreeTerms" name="agreeTerms" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" required>
                <label for="agreeTerms" class="text-sm text-gray-600">
                  Saya setuju dengan <a href="#" class="text-blue-600 hover:text-blue-800 underline">Syarat dan Ketentuan</a> serta <a href="#" class="text-blue-600 hover:text-blue-800 underline">Kebijakan Privasi</a>
                </label>
              </div>

              <div id="registerError" class="text-red-600 text-sm mt-1 hidden"></div>
              <div id="registerSuccess" class="text-green-600 text-sm mt-1 hidden"></div>

              <button type="submit" id="registerBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                </svg>
                Buat Akun
              </button>
            </form>

            <div class="mt-6 text-center">
              <p class="text-sm text-gray-500">© 2024 Peta Talenta. Semua hak dilindungi.</p>
            </div>
          </div>
        </div>
      </div>
    `;
  }



  attachEventListeners() {
    // Remove existing listeners
    this.container.removeEventListener('click', this.handleClick);
    this.container.removeEventListener('submit', this.handleSubmit);
    
    // Add new listeners
    this.container.addEventListener('click', this.handleClick.bind(this));
    this.container.addEventListener('submit', this.handleSubmit.bind(this));
  }

  handleClick(event) {
    const { target } = event;

    if (target.id === 'showRegisterBtn' || target.id === 'registerTab') {
      this.switchToTab('register');
    } else if (target.id === 'showLoginBtn' || target.id === 'loginTab') {
      this.switchToTab('login');
    }
  }

  // Switch between login and register tabs without full re-render
  switchToTab(formType) {
    this.currentForm = formType;

    // Update tab active states
    const loginTab = DOMUtils.getElementById('loginTab');
    const registerTab = DOMUtils.getElementById('registerTab');
    const loginForm = DOMUtils.getElementById('loginForm');
    const registerForm = DOMUtils.getElementById('registerForm');
    const formTitle = document.querySelector('h2');
    const formSubtitle = document.querySelector('p.text-gray-600');

    if (loginTab && registerTab && loginForm && registerForm && formTitle && formSubtitle) {
      // Update tab states
      if (formType === 'login') {
        // Update tab classes
        loginTab.className = 'flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer bg-white text-blue-600 font-medium shadow-sm';
        registerTab.className = 'flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer text-gray-600 hover:text-gray-800';

        loginForm.classList.remove('hidden');
        registerForm.classList.add('hidden');
        formTitle.textContent = 'Selamat Datang Kembali';
        formSubtitle.textContent = 'Masuk ke akun Anda untuk melanjutkan';
      } else {
        // Update tab classes
        loginTab.className = 'flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer text-gray-600 hover:text-gray-800';
        registerTab.className = 'flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer bg-white text-blue-600 font-medium shadow-sm';

        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');
        formTitle.textContent = 'Buat Akun Anda';
        formSubtitle.textContent = 'Bergabunglah dengan Peta Talenta hari ini';
      }

      // Clear any error messages
      this.clearAllErrors();
    }
  }

  // Clear all error and success messages
  clearAllErrors() {
    const errorElements = this.container.querySelectorAll('.error-message, .success-message');
    errorElements.forEach(el => {
      el.classList.add('hidden');
      el.textContent = '';
    });
  }

  async handleSubmit(event) {
    event.preventDefault();
    const form = event.target;

    if (form.id === 'loginForm') {
      await this.handleLogin(form);
    } else if (form.id === 'registerForm') {
      await this.handleRegister(form);
    }
  }

  async handleLogin(form) {
    const formData = DOMUtils.getFormData(form);
    const submitBtn = DOMUtils.getElementById('loginBtn');
    const errorEl = DOMUtils.getElementById('loginError');
    const successEl = DOMUtils.getElementById('loginSuccess');
    
    // Clear previous messages
    DOMUtils.hideError(errorEl);
    DOMUtils.hideSuccess(successEl);
    
    // Validate form
    const validation = Validator.validateForm(formData, {
      email: VALIDATION_RULES.email,
      password: VALIDATION_RULES.password
    });
    
    if (!validation.isValid) {
      this.showFieldErrors('login', validation.errors);
      return;
    }
    
    try {
      DOMUtils.setLoading(submitBtn, true);
      
      const result = await authService.login(formData.email, formData.password);
      
      if (result.success) {
        DOMUtils.showSuccess(successEl, 'Login berhasil!');
        setTimeout(() => {
          if (this.onAuthSuccess) {
            this.onAuthSuccess();
          }
        }, 1000);
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }

  async handleRegister(form) {
    const formData = DOMUtils.getFormData(form);
    const submitBtn = DOMUtils.getElementById('registerBtn');
    const errorEl = DOMUtils.getElementById('registerError');
    const successEl = DOMUtils.getElementById('registerSuccess');

    // Clear previous messages
    DOMUtils.hideError(errorEl);
    DOMUtils.hideSuccess(successEl);

    // Check if passwords match
    if (formData.password !== formData.confirmPassword) {
      const confirmPasswordError = DOMUtils.getElementById('confirmPasswordError');
      DOMUtils.showError(confirmPasswordError, 'Kata sandi tidak cocok');
      return;
    }

    // Check terms agreement
    const agreeTermsCheckbox = DOMUtils.getElementById('agreeTerms');
    if (!agreeTermsCheckbox || !agreeTermsCheckbox.checked) {
      DOMUtils.showError(errorEl, 'Anda harus menyetujui Syarat dan Ketentuan');
      return;
    }

    // Validate form
    const validation = Validator.validateForm(formData, {
      email: VALIDATION_RULES.email,
      password: VALIDATION_RULES.password
    });

    if (!validation.isValid) {
      this.showFieldErrors('register', validation.errors);
      return;
    }

    try {
      DOMUtils.setLoading(submitBtn, true);

      const result = await authService.register(formData.email, formData.password);

      if (result.success) {
        DOMUtils.showSuccess(successEl, 'Pendaftaran berhasil!');
        setTimeout(() => {
          if (this.onAuthSuccess) {
            this.onAuthSuccess();
          }
        }, 1000);
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }





  showFieldErrors(formType, errors) {
    Object.entries(errors).forEach(([field, message]) => {
      const errorEl = DOMUtils.getElementById(`${formType}${field.charAt(0).toUpperCase() + field.slice(1)}Error`) ||
                     DOMUtils.getElementById(`${field}Error`);
      if (errorEl) {
        DOMUtils.showError(errorEl, message);
      }
    });
  }

  // Public method to switch forms
  switchToForm(formType) {
    this.currentForm = formType;
    this.render();
    this.attachEventListeners();
  }

  // Public method to check auth state and show appropriate form
  checkAuthState() {
    if (authService.isAuthenticated()) {
      // If authenticated, trigger callback to show profile page
      if (this.onAuthSuccess) {
        this.onAuthSuccess();
      }
    } else {
      this.currentForm = 'login';
      this.render();
      this.attachEventListeners();
    }
  }
}
