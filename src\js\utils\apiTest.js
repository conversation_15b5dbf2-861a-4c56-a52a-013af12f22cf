// API Test utility untuk testing koneksi
import { API_CONFIG } from '../config/api.js';

export class APITest {
  static async testConnection() {
    try {
      console.log('Testing API connection to:', API_CONFIG.BASE_URL);
      
      // Test dengan endpoint yang tidak me<PERSON><PERSON>an auth
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: API_CONFIG.HEADERS,
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpassword123'
        })
      });
      
      console.log('API Response Status:', response.status);
      
      if (response.status === 404) {
        console.warn('API endpoint not found - this is expected for testing');
        return true; // Server is reachable
      }
      
      const data = await response.json();
      console.log('API Response:', data);
      
      return true;
    } catch (error) {
      console.error('API Connection Error:', error);
      return false;
    }
  }
  
  static async testRegister(email = '<EMAIL>', password = 'testpass123') {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.AUTH.REGISTER}`, {
        method: 'POST',
        headers: API_CONFIG.HEADERS,
        body: JSON.stringify({ email, password })
      });
      
      const data = await response.json();
      console.log('Register Test Response:', data);
      
      return { success: response.ok, data, status: response.status };
    } catch (error) {
      console.error('Register Test Error:', error);
      return { success: false, error: error.message };
    }
  }
}

// Auto-test connection when in development
if (import.meta.env.DEV) {
  console.log('Development mode - testing API connection...');
  APITest.testConnection();
}
