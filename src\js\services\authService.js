import { API_CONFIG, getAuthHeaders } from '../config/api.js';
import { Validator } from '../utils/validation.js';

class AuthService {
  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL;
  }

  // Register new user
  async register(email, password) {
    try {
      // Normalize email and sanitize inputs
      const normalizedEmail = Validator.normalizeEmail(email);
      const sanitizedPassword = Validator.sanitizeInput(password);

      console.log('Attempting registration to:', `${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.REGISTER}`);
      console.log('Registration data:', { email: normalizedEmail, password: '***' });

      const response = await fetch(`${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.REGISTER}`, {
        method: 'POST',
        headers: API_CONFIG.HEADERS,
        body: JSON.stringify({ email: normalizedEmail, password: sanitizedPassword })
      });

      const data = await response.json();

      if (!response.ok) {
        // Provide more specific error messages based on status code
        let errorMessage = data.message || 'Registration failed';
        if (response.status === 400) {
          errorMessage = 'Data yang dimasukkan tidak valid';
        } else if (response.status === 409) {
          errorMessage = 'Email sudah terdaftar';
        } else if (response.status === 429) {
          errorMessage = 'Terlalu banyak percobaan registrasi. Silakan coba lagi nanti';
        } else if (response.status >= 500) {
          errorMessage = 'Server sedang bermasalah. Silakan coba lagi nanti';
        }
        throw new Error(errorMessage);
      }

      // Store token if registration successful
      if (data.success && data.data.token) {
        localStorage.setItem('authToken', data.data.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
      }

      return data;
    } catch (error) {
      throw new Error(error.message || 'Network error occurred');
    }
  }

  // Login user
  async login(email, password) {
    try {
      // Normalize email and sanitize inputs
      const normalizedEmail = Validator.normalizeEmail(email);
      const sanitizedPassword = Validator.sanitizeInput(password);

      console.log('Attempting login to:', `${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.LOGIN}`);
      console.log('Login data:', { email: normalizedEmail, password: '***' });

      const response = await fetch(`${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.LOGIN}`, {
        method: 'POST',
        headers: API_CONFIG.HEADERS,
        body: JSON.stringify({ email: normalizedEmail, password: sanitizedPassword })
      });

      const data = await response.json();

      if (!response.ok) {
        // Provide more specific error messages based on status code
        let errorMessage = data.message || 'Login failed';
        if (response.status === 401) {
          errorMessage = 'Email atau kata sandi salah';
        } else if (response.status === 429) {
          errorMessage = 'Terlalu banyak percobaan login. Silakan coba lagi nanti';
        } else if (response.status >= 500) {
          errorMessage = 'Server sedang bermasalah. Silakan coba lagi nanti';
        }
        throw new Error(errorMessage);
      }

      // Store token if login successful
      if (data.success && data.data.token) {
        localStorage.setItem('authToken', data.data.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
      }

      return data;
    } catch (error) {
      throw new Error(error.message || 'Network error occurred');
    }
  }

  // Get user profile
  async getProfile() {
    try {
      const response = await fetch(`${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.PROFILE}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch profile');
      }

      return data;
    } catch (error) {
      throw new Error(error.message || 'Network error occurred');
    }
  }

  // Update user profile
  async updateProfile(profileData) {
    try {
      const response = await fetch(`${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.PROFILE}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(profileData)
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to update profile');
      }

      // Update stored user data
      if (data.success && data.data.user) {
        localStorage.setItem('user', JSON.stringify(data.data.user));
      }

      return data;
    } catch (error) {
      throw new Error(error.message || 'Network error occurred');
    }
  }

  // Change password
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await fetch(`${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.CHANGE_PASSWORD}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify({ currentPassword, newPassword })
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to change password');
      }

      return data;
    } catch (error) {
      throw new Error(error.message || 'Network error occurred');
    }
  }

  // Get token balance
  async getTokenBalance() {
    try {
      const response = await fetch(`${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.TOKEN_BALANCE}`, {
        method: 'GET',
        headers: getAuthHeaders()
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch token balance');
      }

      return data;
    } catch (error) {
      throw new Error(error.message || 'Network error occurred');
    }
  }

  // Delete account
  async deleteAccount() {
    try {
      const response = await fetch(`${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.DELETE_ACCOUNT}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to delete account');
      }

      // Clear local storage
      this.logout();

      return data;
    } catch (error) {
      throw new Error(error.message || 'Network error occurred');
    }
  }

  // Logout user
  async logout() {
    try {
      // Try to call logout endpoint
      await fetch(`${this.baseUrl}${API_CONFIG.ENDPOINTS.AUTH.LOGOUT}`, {
        method: 'POST',
        headers: getAuthHeaders()
      });
    } catch (error) {
      // Continue with local logout even if API call fails
      console.warn('Logout API call failed:', error.message);
    } finally {
      // Always clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!localStorage.getItem('authToken');
  }

  // Get current user from localStorage
  getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
}

export default new AuthService();
