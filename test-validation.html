<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Test</title>
</head>
<body>
    <h1>Validation Test</h1>
    <div id="test-results"></div>
    
    <script type="module">
        import { Validator, VALIDATION_RULES } from './src/js/utils/validation.js';
        import { APITest } from './src/js/utils/apiTest.js';
        
        const resultsDiv = document.getElementById('test-results');
        
        function addResult(test, result, details = '') {
            const div = document.createElement('div');
            div.innerHTML = `
                <p><strong>${test}:</strong> 
                <span style="color: ${result ? 'green' : 'red'}">${result ? 'PASS' : 'FAIL'}</span>
                ${details ? `<br><small>${details}</small>` : ''}
                </p>
            `;
            resultsDiv.appendChild(div);
        }
        
        // Test validation functions
        console.log('Testing validation functions...');
        
        // Test email validation
        try {
            const emailTest = Validator.isValidEmail('<EMAIL>');
            addResult('Email Validation', emailTest, '<EMAIL> should be valid');
        } catch (error) {
            addResult('Email Validation', false, `Error: ${error.message}`);
        }
        
        // Test password validation
        try {
            const passwordTest = Validator.isValidPassword('password123');
            addResult('Password Validation', passwordTest, 'password123 should be valid');
        } catch (error) {
            addResult('Password Validation', false, `Error: ${error.message}`);
        }
        
        // Test validation rules
        try {
            const formData = {
                email: '<EMAIL>',
                password: 'password123'
            };
            
            const validation = Validator.validateForm(formData, {
                email: VALIDATION_RULES.email,
                password: VALIDATION_RULES.password
            });
            
            addResult('Form Validation', validation.isValid, `Errors: ${JSON.stringify(validation.errors)}`);
        } catch (error) {
            addResult('Form Validation', false, `Error: ${error.message}`);
        }
        
        // Test API connection
        try {
            const apiTest = await APITest.testConnection();
            addResult('API Connection', apiTest, 'API connection test completed');
        } catch (error) {
            addResult('API Connection', false, `Error: ${error.message}`);
        }
    </script>
</body>
</html>
