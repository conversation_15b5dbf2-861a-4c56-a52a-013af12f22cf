# Peta Talenta - Sistem Autentikasi

Platform pemetaan dan analisis talenta berbasis AI dengan sistem autentikasi modern menggunakan Vanilla JavaScript, Vite, dan Tailwind CSS dengan desain 2-sisi yang elegan.

## 🎯 Tentang Peta Talenta

Peta Talenta adalah platform inovatif yang membantu organisasi dan individu dalam:
- **Pemetaan Bakat**: Identifikasi dan analisis potensi talenta secara komprehensif
- **Analisis Kompetensi**: Evaluasi keahlian dan kemampuan dengan teknologi AI
- **Pengembangan SDM**: Strategi pengembangan sumber daya manusia yang tepat sasaran
- **Perencanaan Karir**: Panduan pengembangan jalur karir berdasarkan analisis talenta

## 🚀 Fitur Sistem Autentikasi

- **Desain Modern**: Layout 2-sisi dengan branding Peta Talenta di kiri dan form di kanan
- **Responsive**: Tampilan yang optimal di berbagai ukuran layar
- **Validasi Real-time**: Validasi form dengan feedback langsung dalam bahasa Indonesia
- **Keamanan Tinggi**: Proteksi terhadap SQL injection dan sanitasi input otomatis
- **Email Normalization**: Konversi email otomatis ke huruf kecil untuk konsistensi
- **Separation of Concerns**: Pemisahan yang jelas antara logic dan styling
- **API Integration**: Terintegrasi dengan API backend untuk autentikasi yang aman

## 🛠️ Teknologi

- **Vanilla JavaScript**: Tanpa framework, pure JavaScript
- **Vite**: Build tool modern untuk development yang cepat
- **Tailwind CSS**: Utility-first CSS framework
- **Modular Architecture**: Struktur kode yang terorganisir

## 📁 Struktur Proyek

```
src/
├── js/
│   ├── components/
│   │   └── AuthForm.js          # Komponen utama auth
│   ├── services/
│   │   └── authService.js       # Service untuk API calls
│   ├── utils/
│   │   ├── dom.js              # Utility untuk DOM manipulation
│   │   └── validation.js       # Utility untuk validasi
│   └── config/
│       └── api.js              # Konfigurasi API
├── style.css                   # Styling dengan Tailwind
└── main.js                     # Entry point aplikasi
```

## 🎨 Fitur Autentikasi

### Masuk (Login)
- Validasi email dan kata sandi dengan keamanan tinggi
- Normalisasi email otomatis ke huruf kecil
- Sanitasi input untuk mencegah SQL injection
- Error handling dengan pesan yang jelas dalam bahasa Indonesia
- Loading state saat proses login

### Daftar (Register)
- Validasi format email dengan normalisasi otomatis
- Validasi kekuatan kata sandi (min 8 karakter, huruf + angka)
- Konfirmasi kecocokan kata sandi
- Persetujuan syarat dan ketentuan
- Feedback validasi real-time dalam bahasa Indonesia
- Sanitasi input untuk keamanan maksimal

### Manajemen Profil
- Tampilan informasi pengguna
- Update profil (username, nama lengkap, tanggal lahir, jenis kelamin, ID sekolah)
- Ganti kata sandi dengan validasi keamanan
- Tampilan saldo token
- Penghapusan akun

## 🔧 Setup dan Instalasi

1. **Clone atau setup proyek**
   ```bash
   npm create vite@latest . -- --template vanilla
   ```

2. **Install dependencies**
   ```bash
   npm install
   npm install -D tailwindcss postcss autoprefixer
   ```

3. **Setup Tailwind CSS**
   ```bash
   npx tailwindcss init -p
   ```

4. **Jalankan development server**
   ```bash
   npm run dev
   ```

5. **Buka browser**
   ```
   http://localhost:5173
   ```

## 🌐 API Integration

Aplikasi ini terintegrasi dengan API backend di `https://api.chhrone.web.id` dengan endpoint:

- `POST /api/auth/register` - Registrasi user baru
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Ambil data profile
- `PUT /api/auth/profile` - Update profile
- `PUT /api/auth/change-password` - Ganti password
- `GET /api/auth/token-balance` - Cek saldo token
- `DELETE /api/auth/account` - Hapus akun
- `POST /api/auth/logout` - Logout user

## 🎯 Validasi

### Email
- Format email yang valid
- Maksimal 255 karakter

### Password
- Minimal 8 karakter
- Harus mengandung minimal 1 huruf dan 1 angka

### Username
- Hanya alphanumeric
- 3-100 karakter

### Profile Fields
- Full name: maksimal 100 karakter
- Date of birth: format ISO, tidak boleh tanggal masa depan
- Gender: 'male' atau 'female'
- School ID: integer positif

## 🎨 Styling

Menggunakan Tailwind CSS dengan custom components:

- `.btn-primary` - Tombol utama
- `.btn-secondary` - Tombol sekunder
- `.input-field` - Input field styling
- `.form-group` - Group form styling
- `.error-message` - Pesan error
- `.success-message` - Pesan sukses

## 🔒 Security

- Token disimpan di localStorage
- Automatic token attachment untuk authenticated requests
- Logout membersihkan semua data lokal
- Validasi client-side dan server-side

## 📱 Responsive Design

- Desktop: Layout 2-sisi penuh
- Tablet: Layout adaptif
- Mobile: Stack layout untuk kemudahan penggunaan

## 🚀 Build untuk Production

```bash
npm run build
```

File hasil build akan ada di folder `dist/`.

## 📝 Catatan

- Aplikasi ini menggunakan localStorage untuk menyimpan token
- Semua validasi dilakukan di client-side dan server-side
- Error handling yang komprehensif untuk user experience yang baik
- Modular architecture memudahkan maintenance dan pengembangan

## 🤝 Kontribusi

Silakan buat pull request atau issue untuk perbaikan dan penambahan fitur.
