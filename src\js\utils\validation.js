// Validation utilities
export class Validator {
  // Input sanitization to prevent injection attacks
  static sanitizeInput(input) {
    if (typeof input !== 'string') return input;

    return input
      .trim()
      .replace(/[<>'"&]/g, (match) => {
        const escapeMap = {
          '<': '&lt;',
          '>': '&gt;',
          "'": '&#x27;',
          '"': '&quot;',
          '&': '&amp;'
        };
        return escapeMap[match];
      });
  }

  // Email normalization (lowercase and sanitize)
  static normalizeEmail(email) {
    if (typeof email !== 'string') return email;
    return this.sanitizeInput(email.toLowerCase().trim());
  }

  // Email validation
  static isValidEmail(email) {
    const normalizedEmail = this.normalizeEmail(email);
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(normalizedEmail) && normalizedEmail.length <= 255;
  }

  // Password validation (minimum 8 characters, at least one letter and one number)
  static isValidPassword(password) {
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
  }

  // Username validation (alphanumeric only, 3-100 characters)
  static isValidUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9]{3,100}$/;
    return usernameRegex.test(username);
  }

  // Full name validation (maximum 100 characters)
  static isValidFullName(fullName) {
    return fullName && fullName.trim().length > 0 && fullName.length <= 100;
  }

  // Date validation (ISO format, not future date)
  static isValidDateOfBirth(dateStr) {
    const date = new Date(dateStr);
    const today = new Date();
    return date instanceof Date && !isNaN(date) && date <= today;
  }

  // Gender validation
  static isValidGender(gender) {
    return ['male', 'female'].includes(gender);
  }

  // School ID validation (positive integer)
  static isValidSchoolId(schoolId) {
    return Number.isInteger(schoolId) && schoolId > 0;
  }

  // Generic required field validation
  static isRequired(value) {
    return value !== null && value !== undefined && value.toString().trim() !== '';
  }

  // Form validation helper
  static validateForm(fields, rules) {
    const errors = {};

    for (const [fieldName, value] of Object.entries(fields)) {
      const fieldRules = rules[fieldName] || [];
      
      for (const rule of fieldRules) {
        if (rule.required && !this.isRequired(value)) {
          errors[fieldName] = rule.message || `${fieldName} is required`;
          break;
        }
        
        if (value && rule.validator && !rule.validator(value)) {
          errors[fieldName] = rule.message || `${fieldName} is invalid`;
          break;
        }
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}

// Pre-defined validation rules
export const VALIDATION_RULES = {
  email: [
    { required: true, message: 'Email wajib diisi' },
    { validator: Validator.isValidEmail, message: 'Masukkan alamat email yang valid' }
  ],
  password: [
    { required: true, message: 'Kata sandi wajib diisi' },
    { validator: Validator.isValidPassword, message: 'Kata sandi minimal 8 karakter dengan setidaknya satu huruf dan satu angka' }
  ],
  username: [
    { validator: Validator.isValidUsername, message: 'Username harus 3-100 karakter alfanumerik' }
  ],
  fullName: [
    { validator: Validator.isValidFullName, message: 'Nama lengkap harus 1-100 karakter' }
  ],
  dateOfBirth: [
    { validator: Validator.isValidDateOfBirth, message: 'Masukkan tanggal lahir yang valid' }
  ],
  gender: [
    { validator: Validator.isValidGender, message: 'Jenis kelamin harus pria atau wanita' }
  ],
  schoolId: [
    { validator: Validator.isValidSchoolId, message: 'ID sekolah harus berupa angka positif' }
  ]
};
